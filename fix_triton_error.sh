#!/bin/bash

# 修复Triton CUDA内核启动失败的脚本
echo "=== 修复Triton CUDA内核错误 ==="

# 1. 禁用可能导致问题的特性
export VLLM_USE_V1=0
export VLLM_DISABLE_EXPERT_PARALLEL=1
export VLLM_USE_DEEP_GEMM=0
export VLLM_DISABLE_ASYNC_OUTPUT_PROC=1

# 2. 强制使用CPU fallback for grouped_topk
export VLLM_DISABLE_TRITON_GROUPED_TOPK=1

# 3. CUDA编译设置
export CUDA_LAUNCH_BLOCKING=1
export TORCH_USE_CUDA_DSA=1

# 4. Triton设置
export TRITON_DISABLE_LINE_INFO=1
export TRITON_CACHE_DIR=/tmp/triton_cache
mkdir -p $TRITON_CACHE_DIR

# 5. 清理GPU内存
echo "清理GPU内存..."
nvidia-smi --gpu-reset || true
sleep 2

# 6. 设置保守的内存分配
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128

echo "环境变量已设置，建议使用以下命令启动："
echo "vllm serve /models/DeepSeek-R1-0528 \\"
echo "  --trust-remote-code \\"
echo "  --tensor-parallel-size 2 \\"
echo "  --dtype bfloat16 \\"
echo "  --max-model-len 4096 \\"
echo "  --max-num-seqs 8 \\"
echo "  --gpu-memory-utilization 0.6 \\"
echo "  --enforce-eager \\"
echo "  --disable-frontend-multiprocessing"
