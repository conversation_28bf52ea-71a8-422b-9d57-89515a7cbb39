#!/usr/bin/env python3
"""
临时补丁：绕过grouped_topk的Triton内核问题
使用CPU fallback实现
"""

import torch
import torch.nn.functional as F
from typing import Optional, <PERSON><PERSON>

def cpu_grouped_topk(
    hidden_states: torch.Tensor,
    gating_output: torch.Tensor,
    topk: int,
    renormalize: bool,
    num_expert_group: int = 0,
    topk_group: int = 0,
    scoring_func: str = "softmax",
    e_score_correction_bias: Optional[torch.Tensor] = None
) -> Tuple[torch.Tensor, torch.Tensor]:
    """
    CPU实现的grouped_topk，避免Triton内核问题
    """
    print("使用CPU fallback for grouped_topk")
    
    # 移到CPU进行计算
    gating_cpu = gating_output.cpu()
    
    if scoring_func == "softmax":
        scores = torch.softmax(gating_cpu, dim=-1)
    elif scoring_func == "sigmoid":
        scores = gating_cpu.sigmoid()
        if e_score_correction_bias is not None:
            scores = scores + e_score_correction_bias.cpu()
    else:
        raise ValueError(f"Unsupported scoring function: {scoring_func}")
    
    # 执行topk操作
    topk_weights, topk_ids = torch.topk(scores, topk, dim=-1)
    
    if renormalize:
        topk_weights = topk_weights / topk_weights.sum(dim=-1, keepdim=True)
    
    # 移回GPU
    return topk_weights.to(hidden_states.device), topk_ids.to(hidden_states.device)

def apply_patch():
    """应用补丁到vllm"""
    try:
        import vllm.model_executor.layers.fused_moe.fused_moe as fused_moe_module
        
        # 备份原函数
        if not hasattr(fused_moe_module, '_original_grouped_topk'):
            fused_moe_module._original_grouped_topk = fused_moe_module.grouped_topk
        
        # 替换为CPU实现
        fused_moe_module.grouped_topk = cpu_grouped_topk
        
        print("✓ grouped_topk补丁已应用")
        return True
        
    except ImportError as e:
        print(f"✗ 无法导入vllm模块: {e}")
        return False
    except Exception as e:
        print(f"✗ 应用补丁失败: {e}")
        return False

def restore_patch():
    """恢复原始函数"""
    try:
        import vllm.model_executor.layers.fused_moe.fused_moe as fused_moe_module
        
        if hasattr(fused_moe_module, '_original_grouped_topk'):
            fused_moe_module.grouped_topk = fused_moe_module._original_grouped_topk
            delattr(fused_moe_module, '_original_grouped_topk')
            print("✓ grouped_topk补丁已恢复")
        
    except Exception as e:
        print(f"✗ 恢复补丁失败: {e}")

if __name__ == "__main__":
    print("=== Triton内核绕过补丁 ===")
    
    if apply_patch():
        print("补丁应用成功，现在可以启动vllm")
        print("使用方法：")
        print("1. 在Python中: import patch_grouped_topk; patch_grouped_topk.apply_patch()")
        print("2. 或者设置环境变量: export PYTHONPATH=/home:$PYTHONPATH")
    else:
        print("补丁应用失败")
