#!/bin/bash

# DeepSeek v2 安全启动脚本 - 避免Triton内核错误
set -e

MODEL_PATH="/models/DeepSeek-R1-0528"

echo "=== DeepSeek v2 安全启动 ==="
echo "时间: $(date)"

# 检查模型路径
if [ ! -d "$MODEL_PATH" ]; then
    echo "错误: 模型路径不存在: $MODEL_PATH"
    exit 1
fi

# 1. 核心修复 - 禁用导致问题的特性
export VLLM_USE_V1=0
export VLLM_DISABLE_EXPERT_PARALLEL=1  # 关键：禁用专家并行
export VLLM_USE_DEEP_GEMM=0            # 禁用DeepGEMM
export VLLM_DISABLE_ASYNC_OUTPUT_PROC=1

# 2. Triton相关修复
export VLLM_DISABLE_TRITON_GROUPED_TOPK=1  # 禁用Triton grouped_topk
export TRITON_DISABLE_LINE_INFO=1
export TRITON_CACHE_DIR=/tmp/triton_cache_safe
mkdir -p $TRITON_CACHE_DIR

# 3. CUDA调试和错误处理
export CUDA_LAUNCH_BLOCKING=1
export TORCH_USE_CUDA_DSA=1

# 4. 内存管理
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128,expandable_segments:True

# 5. NCCL配置（保守设置）
export NCCL_DEBUG=WARN
export NCCL_TIMEOUT=1800
export NCCL_IB_DISABLE=1
export NCCL_P2P_DISABLE=1

# 6. 清理函数
cleanup() {
    echo "清理进程和缓存..."
    pkill -f "vllm serve" || true
    rm -rf /tmp/triton_cache_safe || true
    nvidia-smi --gpu-reset || true
    sleep 2
}

trap cleanup EXIT

# 7. 预清理
cleanup

echo "启动DeepSeek v2（安全模式）..."

# 8. 启动命令 - 最保守的配置
vllm serve "$MODEL_PATH" \
    --trust-remote-code \
    --tensor-parallel-size 2 \
    --dtype bfloat16 \
    --max-model-len 4096 \
    --max-num-seqs 4 \
    --gpu-memory-utilization 0.6 \
    --port 8000 \
    --host 0.0.0.0 \
    --served-model-name deepseek-r1 \
    --disable-frontend-multiprocessing \
    --enforce-eager \
    --disable-log-requests \
    --no-enable-chunked-prefill \
    --no-enable-prefix-caching \
    2>&1 | tee deepseek_safe.log
